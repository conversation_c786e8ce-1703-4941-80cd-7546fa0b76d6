import os
import re
import time
import json
import requests
import threading
import concurrent.futures
from queue import Queue
from typing import List, Dict, Any, Optional, Union

class AI提纯器:
    def __init__(self):
        self.提纯数据目录 = os.path.dirname(os.path.abspath(__file__))
        self.配置目录 = None
        self.api_keys = []
        self.最大重试次数 = 3
        self.api_url = "https://api.siliconflow.cn/v1/chat/completions"
        
    def 设置提纯数据目录(self, 提纯数据目录: str) -> None:
        """设置提纯数据保存目录"""
        self.提纯数据目录 = 提纯数据目录

    def 设置配置目录(self, 配置目录: str) -> None:
        """设置配置目录"""
        self.配置目录 = 配置目录
        self.api_keys = self._加载所有API密钥()

    def _加载所有API密钥(self) -> List[str]:
        """从文件加载所有API密钥"""
        api_keys = []

        if self.配置目录:
            api_keys_file = os.path.join(self.配置目录, "api_keys.txt")
        else:
            api_keys_file = "api_keys.txt"

        try:
            if os.path.exists(api_keys_file):
                with open(api_keys_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            api_keys.append(line)
                print(f"成功加载 {len(api_keys)} 个API密钥")
        except Exception as e:
            print(f"加载API密钥时出错: {e}")

        return api_keys
    
    def _调用API(self, 商品标题: str, api_key: str) -> Optional[str]:
        """调用API提取游戏名称"""
        prompt = "我会给你发送一句商品名字，你需要帮我从中提取出steam的游戏名字，只要回答游戏名字，其他什么话都不要说"

        payload = {
            "model": "deepseek-ai/DeepSeek-V2.5",
            "messages": [
                {"role": "system", "content": prompt},
                {"role": "user", "content": 商品标题}
            ],
            "stream": False,
            "max_tokens": 512,
            "temperature": 0.7,
            "top_p": 0.7,
            "top_k": 50,
            "frequency_penalty": 0.5,
            "n": 1,
            "stop": []
        }

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        for retry in range(self.最大重试次数):
            try:
                response = requests.post(self.api_url, json=payload, headers=headers)
                response.raise_for_status()
                result = response.json()

                if 'choices' in result and len(result['choices']) > 0:
                    游戏名称 = result['choices'][0]['message']['content'].strip()
                    return 游戏名称 if 游戏名称 else "未知"
                else:
                    return "未知"

            except requests.exceptions.RequestException as e:
                if retry == self.最大重试次数 - 1:
                    print(f"API调用失败 (重试{retry + 1}/{self.最大重试次数}): {e}")
                    return "处理失败"
                time.sleep(1)  # 重试前等待1秒
                continue

        return "处理失败"
    
    def _处理单个标题(self, 标题: str, 标题索引: int, 总数量: int, api_key: str, 结果队列: Queue, 进度回调=None) -> None:
        """处理单个商品标题"""
        print(f"正在处理第 {标题索引 + 1}/{总数量} 个标题: {标题}")

        游戏名称 = self._调用API(标题, api_key)

        结果 = {
            "原标题": 标题,
            "游戏名称": 游戏名称
        }

        结果队列.put((标题索引, 结果))
        print(f"提取结果: {游戏名称}")

        # 调用进度回调
        if 进度回调:
            进度回调(标题索引 + 1, 总数量, 标题, 游戏名称)

    def 提纯游戏名称(self, 标题列表: List[str], 进度回调=None) -> List[Dict]:
        """使用多线程AI提取游戏名称"""
        if not self.api_keys:
            print("未设置API密钥，无法使用AI提纯功能")
            return []

        if not 标题列表:
            return []

        print(f"开始处理 {len(标题列表)} 个商品标题，使用 {len(self.api_keys)} 个API密钥")

        # 创建结果队列和结果列表
        结果队列 = Queue()
        结果列表 = [None] * len(标题列表)

        # 使用线程池处理，线程数等于API密钥数量
        线程数 = min(len(self.api_keys), len(标题列表))

        with concurrent.futures.ThreadPoolExecutor(max_workers=线程数) as executor:
            # 为每个标题分配API密钥和提交任务
            futures = []
            for i, 标题 in enumerate(标题列表):
                api_key = self.api_keys[i % len(self.api_keys)]  # 循环使用API密钥
                future = executor.submit(
                    self._处理单个标题,
                    标题, i, len(标题列表), api_key, 结果队列, 进度回调
                )
                futures.append(future)

            # 等待所有任务完成
            concurrent.futures.wait(futures)

        # 从队列中获取结果
        while not 结果队列.empty():
            标题索引, 结果 = 结果队列.get()
            结果列表[标题索引] = 结果

        # 过滤掉None值（如果有的话）
        结果列表 = [结果 for 结果 in 结果列表 if 结果 is not None]

        # 保存提纯结果
        self.保存提纯结果(结果列表)

        print(f"提纯完成，共处理 {len(结果列表)} 个标题")
        return 结果列表

    def 保存提纯结果(self, 结果列表: List[Dict]) -> None:
        """保存提纯结果到文件"""
        if not 结果列表:
            return
            
        # 保存完整JSON结果
        json_文件名 = os.path.join(self.提纯数据目录, f"提纯结果_{time.strftime('%Y%m%d%H%M%S')}.json")
        try:
            with open(json_文件名, 'w', encoding='utf-8') as f:
                json.dump(结果列表, f, ensure_ascii=False, indent=2)
            print(f"提纯结果已保存到: {json_文件名}")
        except Exception as e:
            print(f"保存提纯JSON结果时出错: {e}")
            
        # 保存纯文本游戏名称列表
        txt_文件名 = os.path.join(self.提纯数据目录, "提纯游戏名字.txt")
        try:
            游戏名称集合 = set()
            for 结果 in 结果列表:
                游戏名称 = 结果.get("游戏名称", "").strip()
                if 游戏名称 and 游戏名称 != "未知":
                    游戏名称集合.add(游戏名称)
                    
            with open(txt_文件名, 'w', encoding='utf-8') as f:
                for 游戏名称 in sorted(游戏名称集合):
                    f.write(f"{游戏名称}\n")
                    
            print(f"游戏名称列表已保存到: {txt_文件名}")
        except Exception as e:
            print(f"保存游戏名称列表时出错: {e}")
    
    def 加载提纯结果(self, 文件路径: str) -> List[Dict]:
        """从文件加载提纯结果"""
        try:
            with open(文件路径, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载提纯结果时出错: {e}")
            return []

    def 提取游戏名称(self, 商品标题: str) -> str:
        """提取单个商品标题的游戏名称（兼容性方法）"""
        if not 商品标题:
            return "未知"

        # 调用批量处理方法
        结果列表 = self.提纯游戏名称([商品标题])

        if 结果列表 and len(结果列表) > 0:
            return 结果列表[0].get("游戏名称", "未知")
        else:
            return "未知"